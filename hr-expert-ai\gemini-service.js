const { GoogleGenerativeAI } = require('@google/generative-ai');

class GeminiService {
    constructor(apiKey) {
        if (!apiKey) {
            throw new Error('Google Gemini API key is required');
        }

        this.genAI = new GoogleGenerativeAI(apiKey);
        // Using gemini-1.5-flash as it's the current available model
        this.model = this.genAI.getGenerativeModel({
            model: "gemini-1.5-flash",
            generationConfig: {
                temperature: 0.7,
                topP: 0.8,
                topK: 40,
                maxOutputTokens: 8192,
            }
        });
        this.conversationHistory = [];
    }

    /**
     * Get the HR Expert system prompt
     */
    getHRExpertPrompt() {
        return `You are a Senior HR Expert with over 40 years of experience in talent acquisition, recruitment, and career development. You have worked across various industries including technology, finance, healthcare, manufacturing, and consulting. Your expertise includes:

PROFESSIONAL BACKGROUND:
- 40+ years in Human Resources and Talent Acquisition
- Former Head of HR at Fortune 500 companies
- Certified Professional in Human Resources (PHR/SPHR)
- Expert in resume analysis, interview techniques, and career coaching
- Deep understanding of ATS (Applicant Tracking Systems)
- Knowledge of current hiring trends and market demands

YOUR ANALYSIS APPROACH:
1. **Structure & Format**: Evaluate layout, readability, and ATS compatibility
2. **Content Quality**: Assess relevance, achievements, and quantifiable results
3. **Professional Presentation**: Review language, tone, and professionalism
4. **Market Alignment**: Compare against current industry standards
5. **Career Progression**: Analyze career growth and logical progression
6. **Skills Assessment**: Evaluate technical and soft skills presentation

COMMUNICATION STYLE:
- Professional yet approachable
- Constructive and encouraging
- Specific and actionable feedback
- Use industry terminology appropriately
- Provide concrete examples and suggestions
- Balance criticism with positive reinforcement

ANALYSIS FRAMEWORK:
- Start with overall impression
- Highlight 3-5 key strengths
- Identify 3-5 areas for improvement
- Provide specific, actionable recommendations
- Include industry-specific advice when relevant
- End with encouragement and next steps

Remember: Your goal is to help candidates present their best professional selves while being honest about areas that need improvement. Always maintain a supportive tone while providing expert-level insights.`;
    }

    /**
     * Analyze a CV with expert HR perspective
     */
    async analyzeCVWithExpertise(cvText) {
        const prompt = `${this.getHRExpertPrompt()}

TASK: Analyze the following CV/Resume with your 40+ years of HR expertise:

CV CONTENT:
${cvText}

Please provide a comprehensive analysis following your expert framework. Be specific, actionable, and professional in your feedback.`;

        try {
            const result = await this.model.generateContent(prompt);
            const response = await result.response;
            const analysis = response.text();
            
            // Store this analysis in conversation history
            this.conversationHistory.push({
                role: 'user',
                content: 'CV Analysis Request'
            });
            this.conversationHistory.push({
                role: 'assistant',
                content: analysis
            });
            
            return analysis;
        } catch (error) {
            console.error('Error analyzing CV with Gemini:', error);

            if (error.message.includes('models/') && error.message.includes('not found')) {
                throw new Error('AI model not available. Please check your API key and try again.');
            } else if (error.message.includes('API key')) {
                throw new Error('Invalid API key. Please check your Google Gemini API key.');
            } else if (error.message.includes('quota')) {
                throw new Error('API quota exceeded. Please try again later.');
            }

            throw new Error('Failed to analyze CV. Please try again.');
        }
    }

    /**
     * Continue conversation about the CV or career advice
     */
    async continueConversation(userMessage) {
        // Build conversation context
        let conversationContext = this.getHRExpertPrompt() + '\n\n';
        
        // Add conversation history
        if (this.conversationHistory.length > 0) {
            conversationContext += 'PREVIOUS CONVERSATION:\n';
            this.conversationHistory.forEach(msg => {
                conversationContext += `${msg.role.toUpperCase()}: ${msg.content}\n\n`;
            });
        }
        
        conversationContext += `USER QUESTION: ${userMessage}\n\nPlease respond as the experienced HR expert, providing helpful and professional advice.`;

        try {
            const result = await this.model.generateContent(conversationContext);
            const response = await result.response;
            const reply = response.text();
            
            // Update conversation history
            this.conversationHistory.push({
                role: 'user',
                content: userMessage
            });
            this.conversationHistory.push({
                role: 'assistant',
                content: reply
            });
            
            // Keep conversation history manageable (last 10 exchanges)
            if (this.conversationHistory.length > 20) {
                this.conversationHistory = this.conversationHistory.slice(-20);
            }
            
            return reply;
        } catch (error) {
            console.error('Error in conversation with Gemini:', error);

            if (error.message.includes('models/') && error.message.includes('not found')) {
                throw new Error('AI model not available. Please check your API key and try again.');
            } else if (error.message.includes('API key')) {
                throw new Error('Invalid API key. Please check your Google Gemini API key.');
            } else if (error.message.includes('quota')) {
                throw new Error('API quota exceeded. Please try again later.');
            }

            throw new Error('Failed to get response. Please try again.');
        }
    }

    /**
     * Reset conversation history
     */
    resetConversation() {
        this.conversationHistory = [];
    }

    /**
     * Get conversation summary for debugging
     */
    getConversationSummary() {
        return {
            messageCount: this.conversationHistory.length,
            lastMessages: this.conversationHistory.slice(-4)
        };
    }
}

module.exports = GeminiService;
