import { Star, Mail, MessageCircle, Facebook, Twitter, Linkedin, Instagram, Youtube } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import SocialShare from "./SocialShare";

const Footer = () => {
  const { t } = useTranslation();
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    recursos: [
      { name: t('header.prompts'), href: "/prompts" },
      { name: t('header.aiTools'), href: "/ferramentas-ia" },
      { name: t('header.noCode'), href: "/no-code" },
      { name: t('header.guides'), href: "/guias" },
      { name: t('header.glossary'), href: "/glossario" },
      { name: t('header.blog'), href: "/blog" }
    ],
    categorias: [
      { name: t('categories.Marketing'), href: "/prompts?category=Marketing" },
      { name: t('categories.Produtividade'), href: "/prompts?category=Produtividade" },
      { name: t('categories.Copywriting'), href: "/prompts?category=Copywriting" },
      { name: t('categories.SEO'), href: "/prompts?category=SEO" },
      { name: "Automação", href: "/ferramentas-ia?category=Automação" }
    ],
    empresa: [
      { name: t('footer.aboutUs'), href: "/sobre" },
      { name: t('footer.contact'), href: "/contato" },
      { name: t('footer.privacy'), href: "/privacidade" },
      { name: t('footer.terms'), href: "/termos" },
      { name: t('footer.faq'), href: "/faq" }
    ]
  };

  const socialLinks = [
    { 
      name: "WhatsApp", 
      icon: MessageCircle, 
      href: "https://wa.me/5511999999999?text=Olá! Vim do Prompt Genius AI",
      color: "text-green-600 hover:text-green-700"
    },
    { 
      name: "Facebook", 
      icon: Facebook, 
      href: "https://facebook.com/promptgeniusai",
      color: "text-blue-600 hover:text-blue-700"
    },
    { 
      name: "Twitter", 
      icon: Twitter, 
      href: "https://twitter.com/promptgeniusai",
      color: "text-sky-500 hover:text-sky-600"
    },
    { 
      name: "LinkedIn", 
      icon: Linkedin, 
      href: "https://linkedin.com/company/promptgeniusai",
      color: "text-blue-700 hover:text-blue-800"
    },
    { 
      name: "Instagram", 
      icon: Instagram, 
      href: "https://instagram.com/promptgeniusai",
      color: "text-pink-600 hover:text-pink-700"
    },
    { 
      name: "YouTube", 
      icon: Youtube, 
      href: "https://youtube.com/@promptgeniusai",
      color: "text-red-600 hover:text-red-700"
    }
  ];

  return (
    <footer className="bg-gray-900 text-white">
      {/* Main Footer */}
      <div className="max-w-7xl mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <Star className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold">{t('header.title')}</h3>
                <p className="text-sm text-gray-400">{t('header.subtitle')}</p>
              </div>
            </div>
            <p className="text-gray-300 mb-6 max-w-md">
              {t('footer.description')}
            </p>
            
            {/* Social Share */}
            <div className="mb-6">
              <p className="text-sm text-gray-400 mb-3">{t('footer.shareWith')}</p>
              <SocialShare
                title={`${t('header.title')} - +6000 ${t('footer.aiResources')}`}
                description={t('footer.description')}
                hashtags={["IA", "ChatGPT", "Prompts", "NoCode", "Produtividade"]}
              />
            </div>

            {/* Newsletter */}
            <div>
              <h4 className="font-semibold mb-3">{t('footer.weeklyNewsletter')}</h4>
              <p className="text-sm text-gray-400 mb-3">
                {t('footer.newsletterDescription')}
              </p>
              <div className="flex gap-2">
                <input
                  type="email"
                  placeholder={t('footer.emailPlaceholder')}
                  className="flex-1 px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
                />
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Mail className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Resources Links */}
          <div>
            <h4 className="font-semibold mb-4">{t('footer.resources')}</h4>
            <ul className="space-y-2">
              {footerLinks.recursos.map((link) => (
                <li key={link.name}>
                  <Link
                    to={link.href}
                    className="text-gray-400 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Categories Links */}
          <div>
            <h4 className="font-semibold mb-4">{t('footer.categories')}</h4>
            <ul className="space-y-2">
              {footerLinks.categorias.map((link) => (
                <li key={link.name}>
                  <Link
                    to={link.href}
                    className="text-gray-400 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company Links */}
          <div>
            <h4 className="font-semibold mb-4">{t('footer.company')}</h4>
            <ul className="space-y-2 mb-6">
              {footerLinks.empresa.map((link) => (
                <li key={link.name}>
                  <Link
                    to={link.href}
                    className="text-gray-400 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>

            {/* Social Media */}
            <div>
              <h4 className="font-semibold mb-3">{t('footer.socialMedia')}</h4>
              <div className="grid grid-cols-3 gap-2">
                {socialLinks.map((social) => {
                  const IconComponent = social.icon;
                  return (
                    <a
                      key={social.name}
                      href={social.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`p-2 bg-gray-800 rounded-md hover:bg-gray-700 transition-colors ${social.color}`}
                      title={social.name}
                    >
                      <IconComponent className="h-4 w-4" />
                    </a>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-800">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-sm text-gray-400 mb-4 md:mb-0">
              © {currentYear} {t('header.title')}. {t('footer.allRightsReserved')}
            </div>
            <div className="flex items-center space-x-6 text-sm text-gray-400">
              <span>{t('footer.madeInBrazil')}</span>
              <span>•</span>
              <span>{t('footer.aiResources')}</span>
              <span>•</span>
              <span>{t('footer.dailyUpdated')}</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
