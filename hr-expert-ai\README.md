# HR Expert AI - CV Analysis System

An AI-powered HR expert system that analyzes CVs/resumes with 40+ years of professional experience simulation using Google Gemini AI.

## Features

- 🤖 **AI HR Expert**: Acts like a seasoned HR professional with decades of experience
- 📄 **Multi-format Support**: Analyzes PDF, DOC, and DOCX files
- 💬 **Simple Chat Interface**: Easy-to-use conversational interface
- 🔍 **Comprehensive Analysis**: Detailed CV review with actionable feedback
- ⚡ **Google Gemini Powered**: Leverages advanced AI for intelligent analysis

## Quick Start

1. **Clone and Install**
   ```bash
   cd hr-expert-ai
   npm install
   ```

2. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env and add your Google Gemini API key
   ```

3. **Get Google Gemini API Key**
   - Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Create a new API key
   - Add it to your `.env` file

4. **Test Gemini Connection** (Optional but recommended)
   ```bash
   npm run test-gemini
   ```

5. **Run the Application**
   ```bash
   npm start
   # or for development
   npm run dev
   ```

6. **Access the Interface**
   - Open your browser to `http://localhost:3000`
   - Upload a CV and start the analysis

## How It Works

1. **Upload CV**: Drag and drop or select your CV file
2. **AI Analysis**: The system extracts text and sends it to Google Gemini
3. **Expert Review**: Receive detailed feedback as if from a 40+ year HR veteran
4. **Actionable Insights**: Get specific recommendations for improvement

## Supported File Types

- PDF (.pdf)
- Microsoft Word (.doc, .docx)

## API Endpoints

- `GET /` - Main interface
- `POST /analyze-cv` - CV analysis endpoint
- `POST /chat` - Chat with HR expert

## Environment Variables

- `GEMINI_API_KEY` - Your Google Gemini API key
- `PORT` - Server port (default: 3000)
- `MAX_FILE_SIZE` - Maximum upload size in bytes
- `UPLOAD_DIR` - Directory for temporary file storage

## Troubleshooting

### Common Issues

1. **"AI model not available" or "models/gemini-pro not found"**
   - The model name has been updated to `gemini-1.5-flash`
   - Run `npm run test-gemini` to test your connection
   - Ensure your API key is valid and has quota available

2. **"Invalid API key"**
   - Get a new API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Make sure it's correctly set in your `.env` file
   - Check for any extra spaces or characters

3. **"API quota exceeded"**
   - Wait a few minutes and try again
   - Check your API usage in Google AI Studio
   - Consider upgrading your API plan if needed

4. **"Failed to extract text from PDF"**
   - Ensure your PDF contains selectable text (not just images)
   - Try converting image-based PDFs to text-based PDFs

5. **Server connection issues**
   - Check if port 3000 is available
   - Try changing the PORT in your `.env` file
   - Restart the server after making changes

### Testing Your Setup

Run the connection test to verify everything is working:
```bash
npm run test-gemini
```

This will test your API key and model connection.

## License

MIT License - Feel free to use and modify as needed.
