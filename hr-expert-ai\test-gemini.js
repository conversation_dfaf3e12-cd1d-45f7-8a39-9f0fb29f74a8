const { GoogleGenerativeAI } = require('@google/generative-ai');
require('dotenv').config();

async function testGeminiConnection() {
    console.log('🧪 Testing Google Gemini Connection...\n');
    
    // Check if API key exists
    if (!process.env.GEMINI_API_KEY) {
        console.error('❌ GEMINI_API_KEY not found in environment variables');
        console.log('Please set your Google Gemini API key in the .env file');
        return;
    }
    
    try {
        const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
        
        // Test with the current model
        console.log('📡 Testing gemini-1.5-flash model...');
        const model = genAI.getGenerativeModel({ 
            model: "gemini-1.5-flash",
            generationConfig: {
                temperature: 0.7,
                topP: 0.8,
                topK: 40,
                maxOutputTokens: 1000,
            }
        });
        
        const testPrompt = "Hello! Please respond with 'Connection successful' if you can read this message.";
        const result = await model.generateContent(testPrompt);
        const response = await result.response;
        const text = response.text();
        
        console.log('✅ Model Response:', text);
        console.log('\n🎉 Gemini connection test successful!');
        console.log('Your HR Expert AI system is ready to use.');
        
    } catch (error) {
        console.error('❌ Gemini connection test failed:');
        console.error('Error:', error.message);
        
        if (error.message.includes('API key')) {
            console.log('\n💡 Solution: Check your API key in the .env file');
            console.log('Get a new key from: https://makersuite.google.com/app/apikey');
        } else if (error.message.includes('models/') && error.message.includes('not found')) {
            console.log('\n💡 Solution: The model name may have changed');
            console.log('Try updating to a newer model version');
        } else if (error.message.includes('quota')) {
            console.log('\n💡 Solution: API quota exceeded');
            console.log('Wait a moment and try again, or check your API usage');
        }
    }
}

// List available models (if possible)
async function listAvailableModels() {
    try {
        console.log('\n📋 Attempting to list available models...');
        const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
        
        // Note: This might not work with all API versions
        // but it's worth trying for debugging
        const models = await genAI.listModels();
        console.log('Available models:', models);
    } catch (error) {
        console.log('ℹ️  Could not list models (this is normal for some API versions)');
    }
}

// Run the tests
async function runTests() {
    await testGeminiConnection();
    await listAvailableModels();
}

runTests();
